import { useState } from 'react';

const faqs = [
  {
    question: "Is this magic or math?",
    answer: "Both. Paradox is simply higher-order logic in disguise."
  },
  {
    question: "Can small teams really afford cosmic tech?",
    answer: "Yes. Our pay-as-you-grow model keeps cashflow cold while leads run hot—even cautious SMBs are warming up to AI when the ROI is real."
  },
  {
    question: "Will the Agency recruit me?",
    answer: "Decrypt the dossier hidden in your confirmation text. Pass the tests, join the ranks."
  },
  {
    question: "What makes Reality Engineering different?",
    answer: "Think Nick Fury's S.W.O.R.D., but for commerce—an extra-governmental guild safeguarding your market from algorithmic asteroids."
  },
  {
    question: "How do you measure success?",
    answer: "Generative messaging lifts click-throughs 3-4× for early adopters, per HubSpot & Salesforce studies; we wire those gains straight into your bottom line."
  },
  {
    question: "What's the cosmic credentials thing about?",
    answer: "We deploy ops as quietly as Langley but guard your brand integrity with CIA-grade rigor—'accomplishing what others cannot.'"
  }
];

export default function FaqAccordion() {
  const [openIndex, setOpenIndex] = useState(null);
  
  const toggleFaq = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  
  return (
    <div className="faq-accordion">
      {faqs.map((faq, index) => (
        <div 
          key={index} 
          className={`faq-item ${openIndex === index ? 'open' : ''}`}
          onClick={() => toggleFaq(index)}
        >
          <div className="faq-question">
            <h3>{faq.question}</h3>
            <span className="toggle-icon">{openIndex === index ? '−' : '+'}</span>
          </div>
          <div className="faq-answer">
            <p>{faq.answer}</p>
          </div>
        </div>
      ))}
      
      <style jsx>{`
        .faq-accordion {
          max-width: 800px;
          margin: 0 auto;
        }
        
        .faq-item {
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          cursor: pointer;
          transition: background 0.3s;
        }

        .faq-item:hover {
          background: rgba(255, 255, 255, 0.02);
        }
        
        .faq-question {
          padding: 1.5rem 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .faq-question h3 {
          margin: 0;
          font-size: 1.2rem;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
          flex: 1;
          text-align: left;
        }
        
        .toggle-icon {
          font-size: 1.5rem;
          color: rgba(168, 178, 255, 0.8);
          font-weight: 300;
          margin-left: 1rem;
          transition: transform 0.3s;
        }

        .faq-item.open .toggle-icon {
          transform: rotate(180deg);
        }
        
        .faq-answer {
          max-height: 0;
          overflow: hidden;
          transition: max-height 0.3s ease, padding 0.3s ease;
          opacity: 0;
        }
        
        .faq-item.open .faq-answer {
          max-height: 200px;
          padding-bottom: 1.5rem;
          opacity: 1;
        }
        
        .faq-answer p {
          margin: 0;
          color: rgba(255, 255, 255, 0.7);
          line-height: 1.6;
          font-size: 1rem;
        }

        @media (max-width: 768px) {
          .faq-question {
            padding: 1.2rem 0;
          }

          .faq-question h3 {
            font-size: 1.1rem;
          }

          .toggle-icon {
            font-size: 1.3rem;
          }

          .faq-answer p {
            font-size: 0.95rem;
          }
        }
      `}</style>
    </div>
  );
}
