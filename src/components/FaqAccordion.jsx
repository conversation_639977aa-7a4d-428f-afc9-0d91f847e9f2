import { useState } from 'react';
import styles from './FaqAccordion.module.css';

const faqs = [
  {
    question: "Is this magic or math?",
    answer: "Both. Paradox is simply higher-order logic in disguise."
  },
  {
    question: "Can small teams really afford cosmic tech?",
    answer: "Yes. Our pay-as-you-grow model keeps cashflow cold while leads run hot—even cautious SMBs are warming up to AI when the ROI is real."
  },
  {
    question: "Will the Agency recruit me?",
    answer: "Decrypt the dossier hidden in your confirmation text. Pass the tests, join the ranks."
  },
  {
    question: "What makes Reality Engineering different?",
    answer: "Think Nick Fury's S.W.O.R.D., but for commerce—an extra-governmental guild safeguarding your market from algorithmic asteroids."
  },
  {
    question: "How do you measure success?",
    answer: "Generative messaging lifts click-throughs 3-4× for early adopters, per HubSpot & Salesforce studies; we wire those gains straight into your bottom line."
  },
  {
    question: "What's the cosmic credentials thing about?",
    answer: "We deploy ops as quietly as Langley but guard your brand integrity with CIA-grade rigor—'accomplishing what others cannot.'"
  }
];

export default function FaqAccordion() {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFaq = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="faq-accordion">
      {faqs.map((faq, index) => (
        <div
          key={index}
          className={`faq-item ${openIndex === index ? 'open' : ''}`}
          onClick={() => toggleFaq(index)}
        >
          <div className="faq-question">
            <h3>{faq.question}</h3>
            <span className="toggle-icon">{openIndex === index ? '−' : '+'}</span>
          </div>
          <div className="faq-answer">
            <p>{faq.answer}</p>
          </div>
        </div>
      ))}


    </div>
  );
}
