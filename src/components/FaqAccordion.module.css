.faqAccordion {
  max-width: 800px;
  margin: 0 auto;
}

.faqItem {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background 0.3s;
}

.faqItem:hover {
  background: rgba(255, 255, 255, 0.02);
}

.faqItem.open .toggleIcon {
  transform: rotate(180deg);
}

.faqItem.open .faqAnswer {
  max-height: 200px;
  padding-bottom: 1.5rem;
  opacity: 1;
}

.faqQuestion {
  padding: 1.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faqQuestion h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
  text-align: left;
}

.toggleIcon {
  font-size: 1.5rem;
  color: rgba(168, 178, 255, 0.8);
  font-weight: 300;
  margin-left: 1rem;
  transition: transform 0.3s;
}

.faqAnswer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  opacity: 0;
}

.faqAnswer p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .faqQuestion {
    padding: 1.2rem 0;
  }

  .faqQuestion h3 {
    font-size: 1.1rem;
  }

  .toggleIcon {
    font-size: 1.3rem;
  }

  .faqAnswer p {
    font-size: 0.95rem;
  }
}
