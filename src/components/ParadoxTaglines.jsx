import { useState, useEffect } from 'react';

// Paradoxical taglines - each contains internal yin-yang tension
const PARADOX_TAGLINES = [
  "Sell like starlight—silent, steady, seen everywhere.",
  "Hack gravity; watch revenue fall upwards.",
  "We automate the mundane so you can author the mythic.",
  "Paperclips to wormholes—one platform.",
  "Your inbox finds customers before they know they exist.",
  "Quantum marketing, classical results.",
  "Where hot logic meets cool mysticism.",
  "Turning your stardust into starlight-level sales.",
  "Cosmic vision, earthly revenue.",
  "Frictionless pipelines, fractal possibilities.",
  "Black-ops precision with cosmic wonder.",
  "Effortless complexity, complex simplicity.",
  "Predictable surprises, surprising predictability.",
  "Intimate scale, scaled intimacy.",
];

export default function ParadoxTaglines() {
  const [currentTagline, setCurrentTagline] = useState(PARADOX_TAGLINES[0]);

  useEffect(() => {
    // Select a random paradoxical tagline on component mount
    const randomIndex = Math.floor(Math.random() * PARADOX_TAGLINES.length);
    setCurrentTagline(PARADOX_TAGLINES[randomIndex]);
  }, []);

  return (
    <div className="paradox-taglines">
      <div className="tagline-container">
        <div className="paradox-tagline">
          <span className="label">PARADOX</span>
          <p>{currentTagline}</p>
        </div>
      </div>
      <p className="note">Each visit spawns a new paradoxical truth, courtesy of our on-page agent.</p>

      <style>{`
        .paradox-taglines {
          margin: 4rem 0;
          text-align: center;
        }

        .tagline-container {
          display: flex;
          justify-content: center;
          margin-bottom: 1rem;
        }

        .paradox-tagline {
          max-width: 600px;
          padding: 2.5rem;
          border-radius: 16px;
          position: relative;
          backdrop-filter: blur(10px);
          transition: transform 0.3s, box-shadow 0.3s;
          background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(255, 255, 255, 0.05) 100%);
          border: 1px solid rgba(168, 178, 255, 0.2);
        }

        .paradox-tagline:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 40px rgba(168, 178, 255, 0.15);
          border-color: rgba(168, 178, 255, 0.4);
        }

        .label {
          position: absolute;
          top: -12px;
          left: 50%;
          transform: translateX(-50%);
          background: linear-gradient(45deg, #000, #1a1a2e);
          padding: 0 15px;
          font-size: 0.8rem;
          color: rgba(168, 178, 255, 0.9);
          font-weight: 700;
          letter-spacing: 2px;
          border-radius: 4px;
        }

        .paradox-tagline p {
          margin: 0;
          font-size: 1.3rem;
          line-height: 1.4;
          color: rgba(255, 255, 255, 0.95);
          font-style: italic;
          font-weight: 400;
        }

        .note {
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.5);
          font-style: italic;
          margin-top: 1.5rem;
        }

        @media (max-width: 768px) {
          .paradox-tagline {
            padding: 2rem;
            margin: 0 1rem;
          }

          .paradox-tagline p {
            font-size: 1.1rem;
          }
        }
      `}</style>
    </div>
  );
}
